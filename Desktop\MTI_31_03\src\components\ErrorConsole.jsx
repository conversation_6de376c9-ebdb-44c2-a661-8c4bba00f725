import React, { useState, useEffect, forwardRef, useRef, useImperative<PERSON><PERSON>le, useMemo, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setCollapse } from "../store/slices/collapse";
import filterIcon from "../assets/newFilter.png";
import { FaSearch, FaSortAlphaDown, FaSortAlphaUp } from "react-icons/fa";
import { clearMessages } from "../store/slices/consoleMsg.js";
import * as XLSX from "xlsx";
import { setConsoleMsgs } from "../store/slices/consoleMsg";
import {
  WarningAmber as AttentionIcon,
  Error as ErrorIcon,
  ReportProblem as WarningIcon,
  Chat as MessagesIcon,
  ShowChart as TradingIcon,
  Delete as ClearLogsIcon,
  FilterList as ClearFilterIcon,
  FileCopy as CopyAllIcon,
  SaveAlt as ExportIcon,
  List as AllLogsIcon,
} from "@mui/icons-material";

// Constants
const MAX_MESSAGES = 1000; // Limit messages to prevent memory issues
const TABLE_HEADERS = [ "timestamp", "logType", "user", "strategy", "portfolio", "msg" ];
const COLUMN_WIDTHS = {
  msg: "30%",
  timestamp: "8%",
  portfolio: "7%",
  strategy: "7%",
  logType: "5.5%",
  user: "5.5%",
  default: "5%"
};

export const ErrorContainer = forwardRef((_, ref) => {
  const dispatch = useDispatch();
  const { collapsed, height } = useSelector((state) => state.collapseReducer);
  const messages = useSelector((state) => state.consoleMsgsReducer.consoleMsgs);

  // Refs
  const containerRef = useRef(null);
  const filterRefs = useRef({});

  // State
  const [ resizing, setResizing ] = useState(false);
  const [ startY, setStartY ] = useState(0);
  const [ startHeight, setStartHeight ] = useState(0);
  const [ tooltipVisible, setTooltipVisible ] = useState(false);
  const [ showFilters, setShowFilters ] = useState({});
  const [ selectedFilters, setSelectedFilters ] = useState({});
  const [ sortConfig, setSortConfig ] = useState({ key: null, direction: "asc" });
  const [ searchTerms, setSearchTerms ] = useState({});
  const [ dropdownSortConfig, setDropdownSortConfig ] = useState({});

  // Memoized values for performance
  const limitedMessages = useMemo(() =>
    messages.slice(0, MAX_MESSAGES), [ messages ]
  );

  const messageCounts = useMemo(() => ({
    attentions: limitedMessages.filter(msg => msg.logType === "ATTENTION").length,
    errors: limitedMessages.filter(msg => msg.logType === "ERROR").length,
    warnings: limitedMessages.filter(msg => msg.logType === "WARNING").length,
    messages: limitedMessages.filter(msg => msg.logType === "MESSAGE").length,
    tradings: limitedMessages.filter(msg => msg.logType === "TRADING").length,
  }), [ limitedMessages ]);

  const uniqueValues = useMemo(() => {
    const values = {};
    TABLE_HEADERS.forEach(header => {
      values[ header ] = [ ...new Set(
        limitedMessages.map(msg => {
          const value = msg[ header ];
          return value == null || value === "" ? "(Blank)" : value.toString().toLowerCase();
        })
      ) ].filter(Boolean);
    });
    return values;
  }, [ limitedMessages ]);

  const filteredMessages = useMemo(() => {
    let filtered = limitedMessages.filter(message =>
      Object.entries(selectedFilters).every(([ key, values ]) =>
        values.length === 0 || values.includes(message[ key ]?.toString().toLowerCase())
      )
    );

    // Apply sorting
    if (sortConfig.key) {
      filtered = [ ...filtered ].sort((a, b) => {
        const aVal = a[ sortConfig.key ] || "";
        const bVal = b[ sortConfig.key ] || "";
        if (aVal < bVal) return sortConfig.direction === "asc" ? -1 : 1;
        if (aVal > bVal) return sortConfig.direction === "asc" ? 1 : -1;
        return 0;
      });
    }

    return filtered;
  }, [ limitedMessages, selectedFilters, sortConfig ]);

  // Optimized message handler with memoization
  const handleMsg = useCallback((Msg) => {
    dispatch((dispatch, getState) => {
      const previousConsoleMsgs = getState().consoleMsgsReducer.consoleMsgs;

      // Limit messages to prevent memory issues
      const limitedPrevious = previousConsoleMsgs.slice(0, MAX_MESSAGES - 1);

      const lastMsg = limitedPrevious[ 0 ];
      if (
        lastMsg &&
        lastMsg.msg === Msg.msg &&
        lastMsg.user === Msg.user &&
        lastMsg.strategy === Msg.strategy &&
        lastMsg.portfolio === Msg.portfolio
      ) {
        dispatch(setConsoleMsgs({
          consoleMsgs: [ Msg, ...limitedPrevious.slice(1) ],
        }));
      } else {
        dispatch(setConsoleMsgs({
          consoleMsgs: [ Msg, ...limitedPrevious ],
        }));
      }
    });
  }, [ dispatch ]);

  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight;
    }
  }, [ messages ]);

  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.style.height = collapsed ? "75px" : `${height}px`;
      containerRef.current.style.overflow = "auto"; // Ensure scrolling for sticky headers
    }
  }, [ collapsed, height, messages.length ]);

  useImperativeHandle(ref, () => ({
    toggleCollapse() {
      const newHeight = collapsed ? 200 : 75;
      dispatch(setCollapse({ height: newHeight, collapsed: !collapsed }));
      if (containerRef.current) {
        containerRef.current.style.height = `${newHeight}px`;
        containerRef.current.style.overflow = "auto";
      }
    },
  }));

  const handleMouseMove = (e) => {
    if (resizing) {
      const newHeight = startHeight - (e.clientY - startY);
      if (newHeight < 75) {
        dispatch(setCollapse({ height: 75, collapsed: true }));
        containerRef.current.style.height = "75px";
        containerRef.current.style.overflow = "auto";
      } else if (newHeight > 250) {
        dispatch(setCollapse({ height: 250, collapsed: false }));
        containerRef.current.style.height = "250px";
        containerRef.current.style.overflow = "auto";
      } else {
        dispatch(setCollapse({ height: newHeight, collapsed: false }));
        containerRef.current.style.height = `${newHeight}px`;
        containerRef.current.style.overflow = "auto";
      }
    }
  };

  const handleMouseUp = () => {
    setResizing(false);
  };

  useEffect(() => {
    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);
    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };
  }, [ resizing ]);

  // Optimized display handler
  const handleDisplay = useCallback((type) => {
    if (type === "logs") {
      setSelectedFilters({});
    } else if (type === "clear") {
      dispatch(clearMessages());
      setSelectedFilters({});
    } else {
      setSelectedFilters({ logType: [ type.toLowerCase() ] });
    }
  }, [ dispatch ]);

  const copyTableToClipboard = () => {
    let tabularData = "Timestamp \t Logtype \t User \t Strategy \t Portfolio \t Message \t \n";
    let excelData = "Timestamp, Logtype, User, Strategy, Portfolio, Message, \n";
    messages.forEach((row) => {
      const rowData = [];
      rowData.push(row[ "timestamp" ] || "");
      rowData.push(row[ "logType" ] || "");
      rowData.push(row[ "user" ] || "");
      rowData.push(row[ "strategy" ] || "");
      rowData.push(row[ "portfolio" ] || "");
      rowData.push(row[ "msg" ] || "");
      tabularData += rowData.join("\t") + "\n";
      excelData += rowData.join(", ") + "\n";
    });
    try {
      const textarea = document.createElement("textarea");
      textarea.value = tabularData;
      document.body.appendChild(textarea);
      textarea.select();
      document.execCommand("copy");
      document.body.removeChild(textarea);
      setTooltipVisible(true);
      setTimeout(() => {
        setTooltipVisible(false);
      }, 2000);
      handleMsg({
        msg: "Error console Table data successfully copied to clipboard.",
        logType: "ERROR",
        timestamp: `${new Date().toLocaleString()}`,
      });
    } catch (error) {
      console.error("Failed to copy table data to clipboard:", error);
    }
  };

  // Optimized sort handler
  const handleSort = useCallback((key) => {
    const direction = sortConfig.key === key && sortConfig.direction === "asc" ? "desc" : "asc";
    setSortConfig({ key, direction });
  }, [ sortConfig ]);

  const toggleFilterPopup = (key) => {
    setShowFilters((prev) => ({ ...prev, [ key ]: !prev[ key ] }));
  };

  const toggleSelectAll = (key) => {
    setSelectedFilters((prev) => {
      const isAllSelected = prev[ key ]?.length === uniqueValues[ key ]?.length;
      return {
        ...prev,
        [ key ]: isAllSelected ? [] : [ ...uniqueValues[ key ] ],
      };
    });
  };

  // Optimized filter functions
  const toggleFilterValue = useCallback((key, value) => {
    setSelectedFilters((prev) => {
      const updatedValues = prev[ key ]?.includes(value)
        ? prev[ key ].filter((item) => item !== value)
        : [ ...(prev[ key ] || []), value ];
      return { ...prev, [ key ]: updatedValues };
    });
  }, []);

  const handleSearchChange = useCallback((key, value) => {
    setSearchTerms((prev) => ({ ...prev, [ key ]: value }));
  }, []);

  const handleMouseDown = useCallback((e) => {
    setResizing(true);
    setStartY(e.clientY);
    setStartHeight(containerRef.current.offsetHeight);
  }, []);

  const handleExport = useCallback(() => {
    const formattedData = limitedMessages.map((item) => {
      const row = {};
      TABLE_HEADERS.forEach((header) => {
        row[ header ] = item[ header ] || "";
      });
      return row;
    });
    const worksheet = XLSX.utils.json_to_sheet(formattedData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Data");
    XLSX.writeFile(workbook, "console_logs.xlsx");
  }, [ limitedMessages ]);

  useEffect(() => {
    const handleClickOutside = (e) => {
      const isOutsideClick = !Object.values(filterRefs.current).some((ref) => ref && ref.contains(e.target));
      if (isOutsideClick) {
        setShowFilters({});
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleFilterClick = useCallback((e) => {
    e.stopPropagation();
  }, []);

  const clearFilters = useCallback(() => {
    setSelectedFilters({});
    setSearchTerms({});
  }, []);

  const handleDropdownSort = useCallback((header, direction) => {
    setDropdownSortConfig((prevConfig) => ({
      ...prevConfig,
      [ header ]: { direction },
    }));
  }, []);

  const getSortedDropdownValues = useCallback((header) => {
    const values = uniqueValues[ header ] || [];
    const currentConfig = dropdownSortConfig[ header ];

    if (currentConfig?.direction === "desc") {
      return values.sort((a, b) => b.localeCompare(a));
    }
    return values.sort((a, b) => a.localeCompare(b));
  }, [ uniqueValues, dropdownSortConfig ]);

  // Memoized log items for performance
  const logItems = useMemo(() => [
    { icon: <AllLogsIcon />, label: "All Logs", onClick: () => handleDisplay("logs") },
    { icon: <AttentionIcon color="warning" />, label: `${messageCounts.attentions} Attention`, onClick: () => handleDisplay("ATTENTION") },
    { icon: <ErrorIcon color="error" />, label: `${messageCounts.errors} Errors`, onClick: () => handleDisplay("ERROR") },
    { icon: <WarningIcon color="warning" />, label: `${messageCounts.warnings} Warnings`, onClick: () => handleDisplay("WARNING") },
    { icon: <MessagesIcon color="info" />, label: `${messageCounts.messages} Messages`, onClick: () => handleDisplay("MESSAGE") },
    { icon: <TradingIcon color="primary" />, label: `${messageCounts.tradings} Trading`, onClick: () => handleDisplay("TRADING") },
    { icon: <ClearLogsIcon color="secondary" />, label: "Clear Logs", onClick: () => handleDisplay("clear") },
    { icon: <ClearFilterIcon />, label: "Clear Filter", onClick: clearFilters },
    { icon: <CopyAllIcon />, label: "Copy All", onClick: copyTableToClipboard },
    { icon: <ExportIcon />, label: "Export", onClick: handleExport },
  ], [ messageCounts, handleDisplay, clearFilters, copyTableToClipboard, handleExport ]);

  return (
    <div
      className="error-container"
      ref={containerRef}
      style={{
        position: "relative",
        height: collapsed ? "75px" : `${height}px`,
        overflow: "auto",
        backgroundColor: "#fff",
      }}
    >
      <div
        id="draggable"
        style={{
          cursor: "row-resize",
          background: "#d8e1ff",
          width: "100%",
          height: "8px",
          position: "sticky",
          top: 0,
          // zIndex: 10,
          userSelect: "none",
        }}
        onMouseDown={handleMouseDown}
      ></div>

      <div
        className="buttons-container"
        style={{
          display: "flex",
          gap: "10px",
          background: "#D8E1FF",
          position: "sticky",
          top: "6px",
          borderRadius: "1px",
          // zIndex: 9,
        }}
      >
        {logItems.map(({ icon, label, onClick }, index) => (
          <div
            key={index}
            style={{ display: "flex", alignItems: "center", cursor: "pointer", gap: "5px" }}
            onClick={onClick}
          >
            {icon}
            <span>{label}</span>
          </div>
        ))}
        {tooltipVisible && (
          <div
            style={{
              position: "absolute",
              bottom: "120%",
              left: "50%",
              transform: "translateX(-50%)",
              backgroundColor: "#333",
              color: "#fff",
              padding: "5px 10px",
              borderRadius: "4px",
              fontSize: "15px",
              opacity: 0.9,
              zIndex: 11,
            }}
          >
            <span style={{ marginRight: "5px" }}>✔</span> Copied
          </div>
        )}
      </div>

      <table
        style={{
          width: "100%",
          borderCollapse: "collapse",
          tableLayout: "fixed",
          height: "30%",
          margin: 0,
          padding: 0,
        }}
      >
        <thead>
          <tr>
            {TABLE_HEADERS.map((header) => (
              <th
                key={header}
                style={{
                  backgroundColor: "#f4f6fb",
                  color: "#333",
                  fontWeight: "bold",
                  textAlign: "center",
                  padding: "3px 6px",
                  borderBottom: "1px solid #ddd",
                  fontSize: "14px",
                  position: "sticky",
                  top: "48px", // Below draggable (8px) + buttons (40px)
                  // zIndex: 8,
                  width: COLUMN_WIDTHS[ header ] || COLUMN_WIDTHS.default,
                }}
              >
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    cursor: "pointer",
                  }}
                  onClick={() => handleSort(header)}
                >
                  <span style={{ marginLeft: "10px" }}>
                    {header.charAt(0).toUpperCase() + header.slice(1)}
                  </span>
                  {sortConfig.key === header && (
                    <span style={{ marginLeft: "4px", height: "20px", width: "25px" }}>
                      {sortConfig.direction === "asc" ? "\u2193" : "\u2191"}
                    </span>
                  )}
                  <img
                    src={filterIcon}
                    alt="Filter"
                    style={{
                      cursor: "pointer",
                      marginLeft: "-2px",
                      width: "25px",
                      height: "25px",
                      opacity: 0.8,
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleFilterPopup(header);
                    }}
                  />
                </div>

                {showFilters[ header ] && (
                  <div
                    ref={(el) => (filterRefs.current[ header ] = el)}
                    style={{
                      position: "absolute",
                      backgroundColor: "white",
                      padding: "10px",
                      boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
                      zIndex: 1000,
                      marginTop: "4px",
                      borderRadius: "4px",
                      maxHeight: "300px",
                      overflowY: "auto",
                      width:
                        header === "msg"
                          ? "300px"
                          : header === "timestamp"
                            ? "200px"
                            : [ "logType", "strategy", "user", "portfolio" ].includes(header)
                              ? "120px"
                              : "120px",
                    }}
                    onClick={(e) => handleFilterClick(e, header)}
                  >
                    <div style={{ padding: "5px" }}>
                      <div
                        style={{
                          display: "flex",
                          justifyContent: "space-between",
                          marginBottom: "8px",
                        }}
                      >
                        <button
                          onClick={() => handleDropdownSort(header, "asc")}
                          style={{
                            background: "none",
                            border: "none",
                            cursor: "pointer",
                            color: dropdownSortConfig[ header ]?.direction === "asc" ? "#007bff" : "#ccc",
                            fontSize: "16px",
                            display: "flex",
                            alignItems: "center",
                          }}
                          title="Sort A to Z"
                        >
                          <FaSortAlphaDown />
                        </button>
                        <button
                          onClick={() => handleDropdownSort(header, "desc")}
                          style={{
                            background: "none",
                            border: "none",
                            cursor: "pointer",
                            color: dropdownSortConfig[ header ]?.direction === "desc" ? "#007bff" : "#ccc",
                            fontSize: "16px",
                            display: "flex",
                            alignItems: "center",
                          }}
                          title="Sort Z to A"
                        >
                          <FaSortAlphaUp />
                        </button>
                      </div>
                    </div>

                    <div style={{ marginBottom: "8px", position: "relative" }}>
                      <input
                        type="text"
                        placeholder="Search..."
                        value={searchTerms[ header ] || ""}
                        onChange={(e) => handleSearchChange(header, e.target.value)}
                        style={{
                          width: "100%",
                          padding: "4px 30px 4px 4px",
                          boxSizing: "border-box",
                          borderRadius: "4px",
                          border: "1px solid #ccc",
                        }}
                      />
                      <FaSearch
                        style={{
                          position: "absolute",
                          right: "8px",
                          top: "50%",
                          transform: "translateY(-50%)",
                          fontSize: "16px",
                          color: "#888",
                        }}
                      />
                    </div>

                    <form style={{ maxHeight: "120px", overflowY: "auto" }}>
                      <div style={{ marginBottom: "8px" }}>
                        <label
                          style={{
                            display: "flex",
                            alignItems: "center",
                            marginBottom: "6px",
                            fontWeight: "normal",
                            marginTop: "2px",
                            padding: "0px",
                          }}
                        >
                          <input
                            type="checkbox"
                            checked={
                              getSortedDropdownValues(header).length > 0 &&
                              selectedFilters[ header ]?.length === getSortedDropdownValues(header).length
                            }
                            onChange={() => {
                              if (getSortedDropdownValues(header).length > 0) {
                                toggleSelectAll(header);
                              }
                            }
                            }
                            style={{ marginRight: "6px" }}
                            disabled={getSortedDropdownValues(header).length === 0}
                          />
                          <span>Select All</span>
                        </label>
                      </div>

                      {[
                        ...getSortedDropdownValues(header)
                          .filter((value) => value === "" || value === undefined)
                          .map((value) => (
                            <div
                              key={value}
                              style={{ display: "flex", alignItems: "center", marginBottom: "6px" }}
                            >
                              <input
                                type="checkbox"
                                checked={selectedFilters[ header ]?.includes(value)}
                                onChange={() => toggleFilterValue(header, value)}
                                style={{ marginRight: "6px" }}
                              />
                              <span style={{ textAlign: "left", fontWeight: "normal" }}>
                                {value === undefined ? "Empty" : ""}
                              </span>
                            </div>
                          )),
                        ...getSortedDropdownValues(header)
                          .filter((value) => value !== "" && value !== undefined)
                          .map((value) => (
                            <div
                              key={value}
                              style={{ display: "flex", alignItems: "center", marginBottom: "6px" }}
                            >
                              <input
                                type="checkbox"
                                checked={selectedFilters[ header ]?.includes(value)}
                                onChange={() => toggleFilterValue(header, value)}
                                style={{ marginRight: "6px" }}
                              />
                              <span style={{ textAlign: "left", fontWeight: "normal" }}>{value}</span>
                            </div>
                          )),
                      ]}
                    </form>
                  </div>
                )}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {filteredMessages.length > 0 &&
            filteredMessages.map((msg, idx) => (
              <tr
                key={idx}
                style={{
                  backgroundColor: idx % 2 === 0 ? "#ffffff" : "#e8e6e6",
                  height: "25px",
                }}
              >
                {TABLE_HEADERS.map((key) => (
                  <td
                    key={key}
                    style={{
                      textAlign: "left",
                      borderBottom: "1px solid #ddd",
                      fontSize: key === "msg" ? "16px" : "14px",
                      wordBreak: "break-word",
                    }}
                  >
                    {msg[ key ] || ""}
                  </td>
                ))}
              </tr>
            ))}

          {filteredMessages.length > 0 &&
            filteredMessages.length < 10 &&
            Array.from({ length: 10 - filteredMessages.length }, (_, idx) => (
              <tr
                key={filteredMessages.length + idx}
                style={{
                  backgroundColor: (filteredMessages.length + idx) % 2 === 0 ? "#ffffff" : "#e8e6e6",
                  height: "25px",
                }}
              >
                {TABLE_HEADERS.map((key) => (
                  <td
                    key={key}
                    style={{
                      textAlign: "left",
                      padding: key === "msg" ? "12px 10px" : "6px 10px",
                      borderBottom: "1px solid #ddd",
                      fontSize: key === "msg" ? "16px" : "14px",
                    }}
                  ></td>
                ))}
              </tr>
            ))}

          {filteredMessages.length === 0 &&
            Array.from({ length: 10 }, (_, idx) => (
              <tr
                key={idx}
                style={{
                  backgroundColor: idx % 2 === 0 ? "#ffffff" : "#e8e6e6",
                  height: "25px",
                }}
              >
                {TABLE_HEADERS.map((key) => (
                  <td
                    key={key}
                    style={{
                      textAlign: "left",
                      padding: key === "msg" ? "12px 10px" : "6px 10px",
                      borderBottom: "1px solid #ddd",
                      fontSize: key === "msg" ? "16px" : "14px",
                    }}
                  ></td>
                ))}
              </tr>
            ))}
        </tbody>
      </table>
    </div>
  );
});