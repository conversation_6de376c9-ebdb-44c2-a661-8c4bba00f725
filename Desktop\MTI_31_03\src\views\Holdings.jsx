import React, { useState, useRef, useEffect, useMemo } from "react";
import "../styles.css";
import {
  OptimizedMarketIndex,
  OptimizedTopNav,
  OptimizedErrorContainer,
  OptimizedLeftNav,
  OptimizedRightNav,
} from "../components/Layout/OptimizedComponenets";
import { useSelector, useDispatch } from "react-redux";
import { setAllSeq, setAllVis } from "../store/slices";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import useClickOutside from "../hooks/useClickOutside";
import TableHeaderWithFilter from "../components/TableHeaderWithFilter";
import filterIcon from "../assets/newFilter.png";

const styles = `
  .middle-main-container {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .main-table {
    flex: 1;
    overflow: auto;
    height: calc(92vh - 100px);
    position: relative;
  }
  .orderflowtable {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
  }
  .orderflowtable thead {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: #D8E1FF;
  }
  .orderflowtable th {
    font-size: 15px;
    padding: 4px 3px;
    text-align: center;
    border-bottom: 1px solid #ddd;
    white-space: normal;
    vertical-align: middle;
    height: auto;
    line-height: 1.1;
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
    min-width: 70px; /* Increased minimum width for better appearance */
    word-break: break-word;
    hyphens: auto;
  }
  .orderflowtable td {
    text-align: center;
    vertical-align: middle;
  }
  .orderflowtable tbody tr:nth-child(even),
  .orderflowtable tbody tr:nth-child(even) input,
  .orderflowtable tbody tr:nth-child(even) select {
    background-color: #E8E6E6;
  }
  .orderflowtable tbody tr:nth-child(odd),
  .orderflowtable tbody tr:nth-child(odd) input,
  .orderflowtable tbody tr:nth-child(odd) select {
    background-color: #FFFFFF;
  }
  .filter-icon {
    margin-left: 2px;
    cursor: pointer;
    font-size: 16px;
    vertical-align: middle;
  }
  .tooltip-container {
    position: relative;
    display: inline-block;
    margin: 0 2px;
  }
`;

function Holdings() {
  const errorContainerRef = useRef(null);
  const dispatch = useDispatch();
  const { collapsed } = useSelector((state) => state.collapseReducer);

  const [ msgs, setMsgs ] = useState([]);
  const handleClearLogs = () => {
    if (msgs.length === 0) return;
    setMsgs([]);
  };

  const allSeqState = useSelector((state) => state.allSeqReducer);
  const allVisState = useSelector((state) => state.allVisReducer);
  const { holdings: data } = useSelector((state) => state.holdingReducer);
  const holdingsCols = [
    "Action",
    "Exchange",
    "Symbol",
    "Avg Price",
    "Buy Value",
    "LTP",
    "Current Value",
    "P&L%",
    "Collateral Qty",
    "T1 Qty",
    "Cns Sell  Quantity",
    "User ID",
    "User Alias",
  ];


  const [ holdingsColVis, setHoldingsColVis ] = useState(() => {
    const initialVisibility = {};
    holdingsCols.forEach(col => {
      initialVisibility[ col ] = true; // Start with all columns visible
    });
    return initialVisibility;
  });


  const [ holdingColsSelectedALL, setHoldingColsSelectedALL ] = useState(false);

  const holdingColSelectALL = () => {
    const newValue = !holdingColsSelectedALL;
    setHoldingColsSelectedALL(newValue);

    // Update visibility for all columns
    const updatedVisibility = {};
    holdingsCols.forEach(col => {
      updatedVisibility[ col ] = !newValue; // Inverse logic - when selected, hide columns
    });

    setHoldingsColVis(updatedVisibility);

    // Update sequence based on visibility
    if (!newValue) {
      setHoldingsSeq(holdingsCols);
    } else {
      setHoldingsSeq([]);
    }
  };

  const [ holdingsSeq, setHoldingsSeq ] = useState(allSeqState.holdingsSeq);

  useEffect(() => {
    setHoldingsSeq(allSeqState.holdingsSeq);
    setHoldingsColVis((prev) => {
      const colVis = {};
      Object.keys(holdingsColVis).forEach((col) => {
        colVis[ col ] = allSeqState.holdingsSeq.includes(col);
      });
      return { ...colVis };
    });
  }, []);

  useEffect(() => {
    dispatch(
      setAllVis({
        ...allVisState,
        holdingsVis: holdingsColVis,
      })
    );
  }, [ holdingsColVis ]);

  useEffect(() => {
    dispatch(
      setAllSeq({
        ...allSeqState,
        holdingsSeq: holdingsSeq,
      })
    );
  }, [ holdingsSeq ]);

  const mappedHoldings = useMemo(() => {
    return data.map((hold) => ({
      Action: "",
      Exchange: hold.Exchange,
      Symbol: hold.Symbol,
      "Avg Price": hold[ "Avg Price" ],
      "Buy Value": hold[ "Buy Value" ],
      LTP: hold.LTP,
      "Current Value": hold[ "Current Value" ],
      "P&L%": hold[ "P&L%" ],
      "Collateral Qty": hold[ "Collateral Qty" ],
      "T1 Qty": hold[ "T1 Qty" ],
      "Cns Sell  Quantity": hold[ "Cns Sell  Quantity" ],
      "User ID": hold[ "User ID" ],
      "User Alias": hold[ "User Alias" ],
    }));
  }, [ data ]);

  const filterPopupRef = useRef(null);
  useClickOutside(filterPopupRef, () => setFilterPopup(null));

  const [ filters, setFilters ] = useState({});
  const [ filterPopup, setFilterPopup ] = useState(null);
  const [ tempFilters, setTempFilters ] = useState({});
  const [ filteredData, setFilteredData ] = useState(mappedHoldings);
  const [ popupPosition, setPopupPosition ] = useState({ top: 0, left: 0 });
  const [ firstFilteredColumn, setFirstFilteredColumn ] = useState(null);

  useEffect(() => {
    if (Object.keys(filters).length > 0) {
      const filteredDataResult = mappedHoldings.filter((row) =>
        Object.keys(filters).every((col) =>
          filters[ col ]?.length > 0 ? filters[ col ].includes(row[ col ]) : true
        )
      );
      setFilteredData(filteredDataResult);
    } else {
      setFilteredData(mappedHoldings);
    }
  }, [ mappedHoldings, filters ]);

  const handleApplyFilter = () => {
    const newFilters = { ...filters, ...tempFilters };
    if (!firstFilteredColumn) {
      setFirstFilteredColumn(filterPopup);
    }
    setFilters(newFilters);
    setFilterPopup(null);
  };

  const getDynamicUniqueValues = (column) => {
    if (!firstFilteredColumn || column === firstFilteredColumn) {
      return Array.from(new Set(mappedHoldings.map((row) => row[ column ])));
    }
    return Array.from(new Set(filteredData.map((row) => row[ column ])));
  };

  const handleFilterToggle = (column, event) => {
    const { top, left, height } = event.target.getBoundingClientRect();
    setFilterPopup(filterPopup === column ? null : column);
    setPopupPosition({ top: top + height, left });
    setTempFilters(filters);
  };

  const handleFilterChange = (column, value) => {
    setTempFilters((prev) => {
      const columnFilters = prev[ column ] || [];
      if (columnFilters.includes(value)) {
        return { ...prev, [ column ]: columnFilters.filter((v) => v !== value) };
      } else {
        return { ...prev, [ column ]: [ ...columnFilters, value ] };
      }
    });
  };

  const handleSelectAll = (column) => {
    const currentOptions = getDynamicUniqueValues(column);
    const selectedOptions = tempFilters[ column ] || [];
    const allSelected = currentOptions.every((opt) => selectedOptions.includes(opt));

    if (allSelected) {
      setTempFilters((prev) => ({ ...prev, [ column ]: [] }));
    } else {
      setTempFilters((prev) => ({ ...prev, [ column ]: [ ...currentOptions ] }));
    }
  };

  const handleCancelFilter = () => {
    setTempFilters(filters);
    setFilterPopup(null);
  };

  const hasColumnData = (row, column) => {
    return row[ column ] !== undefined && row[ column ] !== "";
  };

  return (
    <div>
      <style>{styles}</style>
      <OptimizedMarketIndex />
      <div className="main-section">
        <OptimizedLeftNav />
        <div className="middle-main-container">
          <OptimizedTopNav
            pageCols={holdingsCols}
            colVis={holdingsColVis}
            setColVis={setHoldingsColVis}
            colsSelectedAll={holdingColsSelectedALL}
            setColsSelectedALL={setHoldingColsSelectedALL}
            selectAll={holdingColSelectALL}
            setSeq={setHoldingsSeq}
          />
          <div className="main-table">
            <table className="orderflowtable">
              <thead>
                <tr>
                  {holdingsSeq.map((column) => (
                    <th
                      key={column}
                      style={{
                        fontSize: "13px",
                        padding: "4px 3px",
                        textAlign: "center",
                        backgroundColor: filters[column]?.length > 0 ? "#f0f7ff" : "inherit",
                        borderBottom: filters[column]?.length > 0 ? "2px solid #1976d2" : "inherit",
                        height: "auto",
                        minWidth: column === "P&L%" ? "80px" :
                                 column === "Avg Price" ? "90px" :
                                 column === "Buy Value" ? "90px" :
                                 column === "Current Value" ? "100px" :
                                 column === "Collateral Qty" ? "100px" : "120px",
                        wordBreak: "break-word",
                        whiteSpace: "normal"
                      }}
                    >
                      <div style={{
                        display: "flex",
                        flexDirection: "row",
                        alignItems: "center",
                        justifyContent: "center",
                        position: "relative",
                        padding: "0",
                        margin: "0",
                        gap: "4px",
                        width: "100%"
                      }}>
                        <TableHeaderWithFilter
                          col={column}
                          columnDisplayNames={{}}
                          hasFilter={filters[column]?.length > 0}
                          selectedItems={filters[column]?.length || 0}
                          handleFilterToggle={(e) => handleFilterToggle(column, e)}
                          filterIcon={filterIcon}
                        />
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="tabletbody">
                {filteredData.map((hold, rowIndex) => (
                  <tr key={rowIndex}>
                    {holdingsSeq.map((column) =>
                      hasColumnData(hold, column) ? (
                        <td
                          style={{
                            padding: "4px",
                            width: "auto",
                            color:
                              column === "P&L%" && parseFloat(hold[ column ]) > 0
                                ? "green"
                                : column === "P&L%" && parseFloat(hold[ column ]) < 0
                                  ? "red"
                                  : "black",
                            verticalAlign: "middle"
                          }}
                          key={column}
                        >
                          {column === "Action" ? (
                            <input
                              type="text"
                              style={{ padding: "4px", border: "none", textAlign: "center" }}
                            />
                          ) : (
                            hold[ column ]
                          )}
                        </td>
                      ) : (
                        <td
                          style={{ padding: "4px", width: "auto", verticalAlign: "middle" }}
                          key={column}
                        >
                          {column === "Action" && (
                            <input
                              type="text"
                              style={{ padding: "4px", border: "none", textAlign: "center" }}
                            />
                          )}
                        </td>
                      )
                    )}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div className="add_collapse">
            <button className="hiddenbutton button">Add</button>
            <button
              style={{ zIndex: "0" }}
              onClick={() => {
                errorContainerRef.current.toggleCollapse();
              }}
              className="button"
              id="collapse"
            >
              {collapsed ? "Expand" : "Collapse"}
            </button>
          </div>
          <OptimizedErrorContainer
            ref={errorContainerRef}
            msgs={msgs}
            handleClearLogs={handleClearLogs}
          />
        </div>
        <OptimizedRightNav />
      </div>
      {filterPopup && filterPopup !== "Action" && (
        <div
          ref={filterPopupRef}
          style={{
            position: "absolute",
            top: `${popupPosition.top + 5}px`,
            left: `${popupPosition.left}px`,
            background: "#ffffff",
            border: "1px solid #e0e0e0",
            borderRadius: "8px",
            padding: "10px",
            boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
            zIndex: 1000,
            minWidth: "150px",
          }}
        >
          <div>
            <label
              style={{
                display: "flex",
                alignItems: "center",
                padding: "2px 5px",
                cursor: "pointer",
                fontWeight: "500",
                color: "#333",
                marginBottom: "0",
              }}
            >
              <input
                type="checkbox"
                checked={
                  tempFilters[ filterPopup ] &&
                  getDynamicUniqueValues(filterPopup).every((opt) =>
                    tempFilters[ filterPopup ].includes(opt)
                  )
                }
                onChange={() => handleSelectAll(filterPopup)}
                style={{
                  marginRight: "8px",
                }}
              />
              <span>Select All</span>
            </label>
            <div
              style={{
                maxHeight: "120px",
                overflowY: "auto",
                margin: "0",
                scrollbarWidth: "thin",
                scrollbarColor: "#888 #f1f1f1",
              }}
            >
              {getDynamicUniqueValues(filterPopup).length > 0 ? (
                getDynamicUniqueValues(filterPopup).map((item) => (
                  <div
                    key={item}
                    style={{
                      display: "flex",
                      alignItems: "center",
                      padding: "1px 5px",
                      cursor: "pointer",
                      margin: "0",
                    }}
                  >
                    <input
                      type="checkbox"
                      checked={tempFilters[ filterPopup ]?.includes(item) || false}
                      onChange={() => handleFilterChange(filterPopup, item)}
                      style={{
                        marginRight: "8px",
                      }}
                    />
                    <span style={{ color: "#444" }}>{item}</span>
                  </div>
                ))
              ) : (
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    padding: "2px 5px",
                    color: "#666",
                    fontStyle: "italic",
                    minHeight: "30px",
                  }}
                >
                  <span>No options available</span>
                </div>
              )}
            </div>
          </div>
          <div
            style={{
              marginTop: "15px",
              display: "flex",
              gap: "10px",
              justifyContent: "flex-end",
            }}
          >
            <button
              onClick={handleCancelFilter}
              style={{
                padding: "6px 12px",
                border: "1px solid #dc3545",
                borderRadius: "4px",
                background: "#dc3545",
                cursor: "pointer",
                color: "white",
                transition: "all 0.2s",
              }}
            >
              Cancel
            </button>
            <button
              onClick={handleApplyFilter}
              style={{
                padding: "6px 12px",
                border: "none",
                borderRadius: "4px",
                background: "#007bff",
                color: "white",
                cursor: "pointer",
                transition: "all 0.2s",
              }}
            >
              OK
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default Holdings;