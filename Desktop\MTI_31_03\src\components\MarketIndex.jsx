import React, { useEffect, useState, useCallback, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { updateMarketData } from "../store/slices/marketSlice";
import { setConsoleMsgs } from "../store/slices/consoleMsg";
import { setProfileImage, setProfileImageLoading, clearProfileImage } from "../store/slices/profileImage";
import Cookies from "universal-cookie";
import { useNavigate } from "react-router-dom";
import "../styles.css";
import { fetchWithAuth } from "../utils/api";
import {
  Menu,
  MenuItem,
  IconButton,
  Divider,
  Box,
  Typography,
  Dialog,
  DialogActions,
  Button,
  Slider, // Use @mui/material/Slider
} from "@mui/material";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";
import EmailIcon from "@mui/icons-material/Email";
import LockIcon from "@mui/icons-material/Lock";
import LogoutIcon from "@mui/icons-material/Logout";
import EditIcon from "@mui/icons-material/Edit";
import <PERSON>ropper from "react-easy-crop";

function MarketIndex() {
  const navigate = useNavigate();
  const cookies = new Cookies();
  const mainUser = cookies.get("USERNAME");
  const dispatch = useDispatch();

  const inactivityTimeout = 30 * 60 * 1000;

  // Use Redux for profile image state management
  const { profileImage, isLoading: isLoadingImage } = useSelector((state) => state.profileImageReducer);
  const [ openPopup, setOpenPopup ] = useState(false);
  const [ selectedImage, setSelectedImage ] = useState(null);
  const [ crop, setCrop ] = useState({ x: 0, y: 0 });
  const [ zoom, setZoom ] = useState(1);
  const [ croppedAreaPixels, setCroppedAreaPixels ] = useState(null);
  const fileInputRef = useRef(null);

  // Market feed enhancement states
  const [ priceFlashes, setPriceFlashes ] = useState({});
  const [ lastPrices, setLastPrices ] = useState({});
  const [ isMarketOpen, setIsMarketOpen ] = useState(true);
  const menuAnchorRef = useRef(null);

  useEffect(() => {
    const fetchImage = async () => {
      try {
        // Check localStorage first
        const cachedImage = localStorage.getItem("PROFILE_IMAGE");
        if (cachedImage && cachedImage !== "null" && cachedImage !== "undefined") {
          dispatch(setProfileImage({ profileImage: cachedImage }));
          return;
        }

        // Only fetch from API if we don't have a cached image and we're not already loading
        if (!isLoadingImage) {
          dispatch(setProfileImageLoading({ isLoading: true }));

          const response = await fetchWithAuth(`/api/get_image/${mainUser}`, {
            method: "GET",
          });

          if (response.ok) {
            const imageData = await response.json();
            if (imageData.image) {
              const base64Image = `data:image/jpeg;base64,${imageData.image}`;
              dispatch(setProfileImage({ profileImage: base64Image }));
              localStorage.setItem("PROFILE_IMAGE", base64Image);
            } else {
              // Set null explicitly in localStorage to avoid repeated fetches
              localStorage.setItem("PROFILE_IMAGE", "null");
              dispatch(setProfileImage({ profileImage: null }));
            }
          } else {
            console.error("Failed to fetch image, using default.");
            localStorage.setItem("PROFILE_IMAGE", "null");
            dispatch(setProfileImage({ profileImage: null }));
          }

          dispatch(setProfileImageLoading({ isLoading: false }));
        }
      } catch (error) {
        console.error("Error fetching image:", error);
        localStorage.setItem("PROFILE_IMAGE", "null");
        dispatch(setProfileImage({ profileImage: null }));
        dispatch(setProfileImageLoading({ isLoading: false }));
      }
    };

    // Only fetch if we don't already have a profile image
    if (!profileImage) {
      fetchImage();
    }
  }, [ mainUser, profileImage, isLoadingImage, dispatch ]);

  const logout = async () => {
    try {
      const response = await fetchWithAuth(`/api/app_logout`, {
        method: "POST",
        body: JSON.stringify({ username: mainUser }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to log out.");
      }

      [ "TOKEN", "USERNAME", "session_id", "PROFILE_IMAGE" ].forEach((cookie) =>
        cookies.remove(cookie, { path: "/" })
      );

      // Clear profile image from Redux store
      dispatch(clearProfileImage());
      localStorage.removeItem("PROFILE_IMAGE");

      navigate("/");
      window.location.reload();
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  useEffect(() => {
    let activityTimer;

    const logoutAfterInactivity = () => {
      logout();
    };

    const resetInactivityTimer = () => {
      clearTimeout(activityTimer);
      activityTimer = setTimeout(logoutAfterInactivity, inactivityTimeout);
    };

    document.addEventListener("mousemove", resetInactivityTimer);
    document.addEventListener("keypress", resetInactivityTimer);

    resetInactivityTimer();

    return () => {
      clearTimeout(activityTimer);
      document.removeEventListener("mousemove", resetInactivityTimer);
      document.removeEventListener("keypress", resetInactivityTimer);
    };
  }, [ inactivityTimeout ]);

  const marketData = useSelector((state) => state.marketReducer);

  const handleMsg = useCallback(
    (Msg) => {
      dispatch((dispatch, getState) => {
        const { consoleMsgs } = getState().consoleMsgsReducer;
        const lastMsg = consoleMsgs[ 0 ];
        const isDuplicate =
          lastMsg &&
          lastMsg.msg === Msg.msg &&
          lastMsg.user === Msg.user &&
          lastMsg.strategy === Msg.strategy &&
          lastMsg.portfolio === Msg.portfolio;

        dispatch(
          setConsoleMsgs({
            consoleMsgs: isDuplicate
              ? [ Msg, ...consoleMsgs.slice(1) ]
              : [ Msg, ...consoleMsgs ],
          })
        );
      });
    },
    [ dispatch ]
  );

  // useEffect(() => {
  //   const eventSource = new EventSource(
  //     `http://106.51.129.99:80/tradingview_events/${mainUser}`,
  //     {
  //       headers: {
  //         Authorization: `Bearer ${token}`,
  //       },
  //     }
  //   );

  //   eventSource.onmessage = (event) => {
  //     try {
  //       const responseData = JSON.parse(event.data);

  //       if (typeof responseData === "object" && responseData.mtitest12) {
  //         Object.keys(responseData).forEach((key) => {
  //           const userData = responseData[ key ][ 0 ];
  //           const portfolioName = userData.portfolio_name;
  //           const brokerResponses = userData.broker_responses;

  //           if (brokerResponses.message) {
  //             handleMsg({
  //               msg: brokerResponses.message,
  //               logType: "TRADING",
  //               timestamp: `${new Date().toLocaleString()}`,
  //               user: key,
  //               strategy: "TradingView",
  //               portfolio: portfolioName,
  //             });
  //           } else {
  //             for (const broker in brokerResponses) {
  //               const brokerArray = brokerResponses[ broker ];
  //               brokerArray.forEach((entry) => {
  //                 const messages = entry.message.messages;
  //                 messages.forEach((messageObj) => {
  //                   const messageText = messageObj.message;
  //                   handleMsg({
  //                     msg: messageText,
  //                     logType: "TRADING",
  //                     timestamp: `${new Date().toLocaleString()}`,
  //                     user: broker,
  //                     strategy: "TradingView",
  //                     portfolio: portfolioName,
  //                   });
  //                 });
  //               });
  //             }
  //           }
  //         });
  //       } else if (Array.isArray(responseData)) {
  //         responseData.forEach((userData) => {
  //           const portfolioName = userData.portfolio_name;
  //           const brokerResponses = userData.broker_responses;

  //           for (const broker in brokerResponses) {
  //             const brokerArray = brokerResponses[ broker ];
  //             brokerArray.forEach((entry) => {
  //               const messages = entry.message.messages;
  //               messages.forEach((messageObj) => {
  //                 const messageText = messageObj.message;
  //                 handleMsg({
  //                   msg: messageText,
  //                   logType: "TRADING",
  //                   timestamp: `${new Date().toLocaleString()}`,
  //                   strategy: "TradingView",
  //                   portfolio: portfolioName,
  //                 });
  //               });
  //             });
  //           }
  //         });
  //       } else {
  //         console.error("Unrecognized data format");
  //       }
  //     } catch (error) {
  //       console.error("Error parsing event data:", error);
  //     }
  //   };

  //   eventSource.onerror = () => {
  //     console.error("EventSource failed. Closing connection.");
  //     eventSource.close();
  //   };

  //   return () => {
  //     eventSource.close();
  //   };
  // }, [ token, handleMsg ]);

  const indices = [ "sensex", "nifty50", "niftybank", "finnifty" ];

  const [ anchorEl, setAnchorEl ] = useState(null);
  const [ timeoutId, setTimeoutId ] = useState(null);
  const open = Boolean(anchorEl);

  const handleMenuOpen = (event) => {
    if (timeoutId) clearTimeout(timeoutId);
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    const id = setTimeout(() => {
      setAnchorEl(null);
    }, 200);
    setTimeoutId(id);
  };

  const handleNavigation = (path) => {
    navigate(path);
    setAnchorEl(null);
  };

  const handleImageUpload = (event) => {
    const file = event.target.files[ 0 ];
    if (file) {
      if (!file.type.match("image.*")) {
        console.error("Please upload a valid image file");
        return;
      }
      const reader = new FileReader();
      reader.onloadend = () => {
        setSelectedImage(reader.result);
        console.log("Selected image loaded:", reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const onCropComplete = useCallback((croppedArea, croppedAreaPixels) => {
    setCroppedAreaPixels(croppedAreaPixels);
    console.log("Cropped area pixels:", croppedAreaPixels);
  }, []);

  const getCroppedImg = async (imageSrc, pixelCrop) => {
    const image = new Image();
    image.src = imageSrc;
    await new Promise((resolve) => (image.onload = resolve));

    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");

    canvas.width = pixelCrop.width;
    canvas.height = pixelCrop.height;

    ctx.drawImage(
      image,
      pixelCrop.x,
      pixelCrop.y,
      pixelCrop.width,
      pixelCrop.height,
      0,
      0,
      pixelCrop.width,
      pixelCrop.height
    );

    return new Promise((resolve) => {
      const croppedDataUrl = canvas.toDataURL("image/jpeg");
      console.log("Cropped image generated:", croppedDataUrl);
      resolve(croppedDataUrl);
    });
  };

  const handleSaveCroppedImage = async () => {
    try {
      if (!selectedImage || !croppedAreaPixels) {
        console.error("No image or crop area available");
        return;
      }

      const croppedImg = await getCroppedImg(selectedImage, croppedAreaPixels);
      if (!croppedImg) {
        console.error("Failed to generate cropped image");
        return;
      }

      const base64String = croppedImg.split(",")[ 1 ];
      if (!base64String) {
        console.error("Failed to extract base64 string from cropped image");
        return;
      }

      console.log("Base64 string to upload:", base64String);

      const response = await fetchWithAuth(`/api/upload_image/${mainUser}`, {
        method: "POST",
        body: JSON.stringify({ image: base64String }),
      });

      if (response.ok) {
        console.log("Image uploaded successfully, setting profile image:", croppedImg);
        dispatch(setProfileImage({ profileImage: croppedImg }));
        localStorage.setItem("PROFILE_IMAGE", croppedImg);
        setSelectedImage(null);
        setOpenPopup(false);
      } else {
        const errorData = await response.json();
        console.error("Failed to upload image:", errorData);
      }
    } catch (error) {
      console.error("Error in handleSaveCroppedImage:", error);
    }
  };

  const handleRemoveImage = async () => {
    try {
      const response = await fetchWithAuth(`/api/delete_image/${mainUser}`, {
        method: "POST",
      });

      if (response.ok) {
        dispatch(clearProfileImage());
        localStorage.removeItem("PROFILE_IMAGE");
      } else {
        console.error("Failed to remove image");
      }
    } catch (error) {
      console.error("Error removing image:", error);
    }
    setOpenPopup(false);
  };

  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleEditClick = () => {
    setOpenPopup(true);
  };

  // CSS animations for market feed
  const marketFeedStyles = `
    @keyframes pulse {
      0% {
        opacity: 1;
        transform: scale(1);
      }
      50% {
        opacity: 0.7;
        transform: scale(1.1);
      }
      100% {
        opacity: 1;
        transform: scale(1);
      }
    }

    @keyframes priceFlash {
      0% {
        background-color: transparent;
      }
      50% {
        background-color: rgba(255, 255, 0, 0.3);
      }
      100% {
        background-color: transparent;
      }
    }

    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translateY(-10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .market-card {
      animation: slideIn 0.5s ease-out;
    }

    .price-update {
      animation: priceFlash 0.8s ease-in-out;
    }

    /* Override existing styles with higher specificity */
    .navbar .sensex-container.enhanced-market-feed {
      display: flex !important;
      gap: 12px !important;
      flex-wrap: wrap !important;
      align-items: center !important;
      padding: 5px 20px !important;
      justify-content: flex-start !important;
    }

    .navbar .sensex-container.enhanced-market-feed > div {
      display: block !important;
      flex-direction: column !important;
      gap: 0 !important;
      align-items: stretch !important;
    }
  `;

  return (
    <>
      <style>{marketFeedStyles}</style>
      <nav className="navbar">
        <div className="sensex-container enhanced-market-feed" style={{
          display: "flex",
          gap: "12px",
          flexWrap: "wrap",
          alignItems: "center",
          padding: "5px 20px"
        }}>
          {indices.map((index) => {
            const data = marketData?.marketData?.[ index ];
            const isPositive = data?.ch >= 0;
            const isNegative = data?.ch < 0;

            return (
              <div key={index} className="market-card" style={{
                background: "linear-gradient(135deg, #ffffff 0%, #f8faff 100%)",
                borderRadius: "12px",
                padding: "12px 16px",
                minWidth: "200px",
                boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                border: `2px solid ${isPositive ? '#e8f5e8' : isNegative ? '#ffe8e8' : '#e0e8ff'}`,
                transition: "all 0.3s ease",
                cursor: "pointer",
                position: "relative",
                overflow: "hidden"
              }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = "translateY(-2px)";
                  e.currentTarget.style.boxShadow = "0 4px 16px rgba(0,0,0,0.15)";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = "translateY(0)";
                  e.currentTarget.style.boxShadow = "0 2px 8px rgba(0,0,0,0.1)";
                }}>
                {/* Background pulse effect for updates */}
                <div style={{
                  position: "absolute",
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: isPositive ?
                    "linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(76, 175, 80, 0.05) 100%)" :
                    isNegative ?
                      "linear-gradient(135deg, rgba(244, 67, 54, 0.1) 0%, rgba(244, 67, 54, 0.05) 100%)" :
                      "transparent",
                  borderRadius: "10px"
                }} />

                <div style={{ position: "relative", zIndex: 1 }}>
                  {/* Index name and price */}
                  <div style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginBottom: "8px"
                  }}>
                    <span style={{
                      fontSize: "14px",
                      fontWeight: "700",
                      color: "#2c3e50",
                      letterSpacing: "0.5px"
                    }}>
                      {index.toUpperCase()}
                    </span>
                    <div style={{
                      width: "8px",
                      height: "8px",
                      borderRadius: "50%",
                      background: isPositive ? "#4CAF50" : isNegative ? "#f44336" : "#9e9e9e",
                      boxShadow: `0 0 8px ${isPositive ? "#4CAF50" : isNegative ? "#f44336" : "#9e9e9e"}`,
                      animation: "pulse 2s infinite"
                    }} />
                  </div>

                  {/* Current price */}
                  <div style={{
                    fontSize: "18px",
                    fontWeight: "800",
                    color: "#1a1a1a",
                    marginBottom: "6px",
                    fontFamily: "monospace"
                  }}>
                    ₹{data?.c || "0.00"}
                  </div>

                  {/* Change and percentage */}
                  {data?.ch && (
                    <div style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "8px"
                    }}>
                      <div style={{
                        display: "flex",
                        alignItems: "center",
                        gap: "4px",
                        padding: "4px 8px",
                        borderRadius: "6px",
                        background: isPositive ?
                          "linear-gradient(135deg, #4CAF50 0%, #45a049 100%)" :
                          "linear-gradient(135deg, #f44336 0%, #d32f2f 100%)",
                        color: "white",
                        fontSize: "12px",
                        fontWeight: "600"
                      }}>
                        <span style={{ fontSize: "10px" }}>
                          {isPositive ? "▲" : "▼"}
                        </span>
                        <span>{Math.abs(data.ch)}</span>
                      </div>
                      <div style={{
                        fontSize: "12px",
                        fontWeight: "600",
                        color: isPositive ? "#4CAF50" : "#f44336",
                        padding: "2px 6px",
                        borderRadius: "4px",
                        background: isPositive ? "#e8f5e8" : "#ffe8e8"
                      }}>
                        {isPositive ? "+" : ""}{data.chp}%
                      </div>
                    </div>
                  )}

                  {/* Last update indicator */}
                  <div style={{
                    fontSize: "10px",
                    color: "#666",
                    marginTop: "6px",
                    display: "flex",
                    alignItems: "center",
                    gap: "4px"
                  }}>
                    <span>🕐</span>
                    <span>Live</span>
                  </div>
                </div>
              </div>
            );
          })}

          {/* Market status and controls */}
          <div style={{
            display: "flex",
            alignItems: "center",
            gap: "8px"
          }}>
            {/* Connection status indicator */}
            <div style={{
              display: "flex",
              alignItems: "center",
              gap: "8px",
              padding: "8px 12px",
              background: "linear-gradient(135deg, #f0f4ff 0%, #e8eeff 100%)",
              borderRadius: "8px",
              border: "1px solid #e0e8ff"
            }}>
              <div style={{
                width: "6px",
                height: "6px",
                borderRadius: "50%",
                background: "#4CAF50",
                boxShadow: "0 0 6px #4CAF50",
                animation: "pulse 2s infinite"
              }} />
              <span style={{
                fontSize: "11px",
                color: "#4CAF50",
                fontWeight: "600"
              }}>
                LIVE
              </span>
            </div>

            {/* Market time indicator */}
            <div style={{
              padding: "8px 12px",
              background: isMarketOpen
                ? "linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%)"
                : "linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%)",
              borderRadius: "8px",
              border: isMarketOpen
                ? "1px solid #28a745"
                : "1px solid #dc3545"
            }}>
              <span style={{
                fontSize: "11px",
                color: isMarketOpen ? "#155724" : "#721c24",
                fontWeight: "600"
              }}>
                {isMarketOpen ? "📊 MARKET OPEN" : "🔒 MARKET CLOSED"}
              </span>
            </div>

            {/* Refresh button */}
            <div
              style={{
                padding: "8px 12px",
                background: "linear-gradient(135deg, #17a2b8 0%, #138496 100%)",
                borderRadius: "8px",
                cursor: "pointer",
                transition: "all 0.2s ease",
                border: "none"
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = "scale(1.05)";
                e.currentTarget.style.boxShadow = "0 4px 12px rgba(23, 162, 184, 0.3)";
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = "scale(1)";
                e.currentTarget.style.boxShadow = "none";
              }}
              onClick={() => {
                // Add refresh functionality here
                console.log("Refreshing market data...");
              }}
            >
              <span style={{
                fontSize: "11px",
                color: "white",
                fontWeight: "600"
              }}>
                🔄 REFRESH
              </span>
            </div>
          </div>
        </div>
        <div className="options-div">
          <Box>
            <IconButton
              ref={menuAnchorRef}
              sx={{ padding: 0 }}
              aria-controls={open ? "user-menu" : undefined}
              aria-haspopup="true"
              aria-expanded={open ? "true" : undefined}
              onClick={handleMenuOpen}
            >
              {profileImage ? (
                <img
                  src={profileImage}
                  alt="Profile"
                  style={{ width: 50, height: 50, borderRadius: "50%", border: "1px solid #2071B2" }}
                />
              ) : (
                <AccountCircleIcon sx={{ color: "#2071B2", fontSize: 50 }} />
              )}
            </IconButton>

            <Menu
              id="user-menu"
              anchorEl={anchorEl}
              open={open}
              onClose={handleMenuClose}
              disableAutoFocusItem={true}
              disableRestoreFocus={true}
              anchorOrigin={{
                vertical: "bottom",
                horizontal: "right",
              }}
              transformOrigin={{
                vertical: "top",
                horizontal: "right",
              }}
              PaperProps={{
                sx: {
                  borderRadius: "8px",
                  boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
                  minWidth: "200px",
                },
              }}
            >
              <MenuItem sx={{ padding: "4px 8px", position: "relative" }}>
                <Box
                  display="flex"
                  alignItems="center"
                  justifyContent="space-between"
                  width="100%"
                >
                  <Typography variant="body1">{mainUser}</Typography>
                  <Box sx={{ position: "relative" }}>
                    {profileImage ? (
                      <Box sx={{ position: "relative", cursor: "pointer" }} onClick={handleEditClick}>
                        <img
                          src={profileImage}
                          alt="Profile"
                          style={{ width: 30, height: 30, borderRadius: "50%", border: "1px solid #2071B2" }}
                        />
                        <Box
                          sx={{
                            position: "absolute",
                            right: -3,
                            bottom: 0,
                            width: 16,
                            height: 16,
                            borderRadius: "50%",
                            backgroundColor: "#2071B2",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            border: "1px solid white",
                            boxShadow: "0 1px 2px rgba(0, 0, 0, 0.2)",
                            pointerEvents: "none",
                          }}
                        >
                          <EditIcon sx={{ fontSize: 10, color: "white", border: "1px solid #2071B2" }} />
                        </Box>
                      </Box>
                    ) : (
                      <Box sx={{ position: "relative", cursor: "pointer" }} onClick={handleEditClick}>
                        <AccountCircleIcon sx={{ fontSize: 30, color: "#2071B2" }} />
                        <Box
                          sx={{
                            position: "absolute",
                            bottom: 5,
                            right: -3,
                            width: 16,
                            height: 16,
                            borderRadius: "50%",
                            backgroundColor: "white",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            border: "1px solid white",
                            boxShadow: "0 1px 2px rgba(0, 0, 0, 0.2)",
                            pointerEvents: "none",
                          }}
                        >
                          <EditIcon sx={{ fontSize: 10, color: "black" }} />
                        </Box>
                      </Box>
                    )}
                  </Box>
                </Box>
              </MenuItem>

              <Divider />

              <MenuItem
                onClick={() => handleNavigation("/Change_Password")}
                sx={{ padding: "4px 8px" }}
              >
                <Box
                  display="flex"
                  alignItems="center"
                  justifyContent="space-between"
                  width="100%"
                >
                  <Typography variant="body1">Change Password</Typography>
                  <LockIcon sx={{ fontSize: 30, color: "#2071B2" }} />
                </Box>
              </MenuItem>

              <MenuItem
                onClick={() => handleNavigation("/Subscription")}
                sx={{ padding: "4px 8px" }}
              >
                <Box
                  display="flex"
                  alignItems="center"
                  justifyContent="space-between"
                  width="100%"
                >
                  <Typography variant="body1">Subscription</Typography>
                  <EmailIcon sx={{ fontSize: 30, color: "#2071B2" }} />
                </Box>
              </MenuItem>

              <MenuItem onClick={logout} sx={{ padding: "4px 8px" }}>
                <Box
                  display="flex"
                  alignItems="center"
                  justifyContent="space-between"
                  width="100%"
                >
                  <Typography variant="body1">Logout</Typography>
                  <LogoutIcon sx={{ fontSize: 30, color: "#2071B2" }} />
                </Box>
              </MenuItem>
            </Menu>

            <Dialog open={openPopup} onClose={() => setOpenPopup(false)}>
              <Box sx={{ padding: 2, textAlign: "center" }}>
                {selectedImage ? (
                  <div style={{ position: "relative", width: "300px", height: "300px" }}>
                    <Cropper
                      image={selectedImage}
                      crop={crop}
                      zoom={zoom}
                      aspect={1}
                      cropShape="round"
                      showGrid={false}
                      onCropChange={setCrop}
                      onZoomChange={setZoom}
                      onCropComplete={onCropComplete}
                    />
                    <div style={{ marginTop: "20px" }}>
                      <Typography>Zoom</Typography>
                      <Slider
                        value={zoom}
                        min={1}
                        max={3}
                        step={0.1}
                        onChange={(e, newValue) => setZoom(newValue)}
                        sx={{ width: 200 }}
                      />
                    </div>
                  </div>
                ) : profileImage ? (
                  <img
                    src={profileImage}
                    alt="Profile Preview"
                    style={{ width: 200, height: 200, borderRadius: "50%", marginBottom: 2, border: "1px solid #2071B2" }}
                  />
                ) : (
                  <AccountCircleIcon sx={{ fontSize: 100, color: "#2071B2", marginBottom: 2 }} />
                )}
              </Box>
              <DialogActions sx={{ justifyContent: "center" }}>
                {selectedImage ? (
                  <>
                    <Button
                      onClick={handleSaveCroppedImage}
                      variant="contained"
                      color="primary"
                    >
                      Save
                    </Button>
                    <Button
                      onClick={() => setSelectedImage(null)}
                      variant="contained"
                      color="secondary"
                    >
                      Cancel
                    </Button>
                  </>
                ) : (
                  <>
                    <Button
                      onClick={triggerFileInput}
                      variant="contained"
                      color="primary"
                      startIcon={<EditIcon />}
                    >
                      Change
                    </Button>
                    <Button
                      onClick={handleRemoveImage}
                      variant="contained"
                      color="secondary"
                      startIcon={<AccountCircleIcon />}
                    >
                      Remove
                    </Button>
                  </>
                )}
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  style={{ display: "none" }}
                  tabIndex={-1}
                />
              </DialogActions>
            </Dialog>
          </Box>
        </div>
      </nav>
    </>
  );
}

export default MarketIndex;
