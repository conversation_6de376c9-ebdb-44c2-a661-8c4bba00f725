{"name": "mti-0312", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/styled": "^11.14.0", "@mui/icons-material": "^5.16.14", "@mui/material": "^5.16.14", "@mui/system": "^6.4.3", "@reduxjs/toolkit": "^2.2.1", "@tailwindcss/vite": "^4.1.7", "axios": "^1.7.9", "event-source-polyfill": "^1.0.31", "framer-motion": "^11.18.2", "lodash": "^4.17.21", "papaparse": "^5.4.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-draggable": "^4.4.6", "react-easy-crop": "^5.4.1", "react-icons": "^4.12.0", "react-loader-spinner": "^6.1.6", "react-modal": "^3.16.1", "react-redux": "^9.1.0", "react-router-dom": "^6.21.3", "react-time-picker": "^7.0.0", "react-toastify": "^9.1.3", "universal-cookie": "^6.1.1", "xlsx": "^0.18.5"}, "devDependencies": {"@types/react": "^18.2.43", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "eslint": "^8.55.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.7", "unimported": "^1.31.1", "vite": "^5.0.8"}}